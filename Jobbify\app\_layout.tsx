// Import polyfills first to fix WebCrypto API warning
import 'react-native-get-random-values';

import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useFonts } from 'expo-font';
import { Slot, useSegments, useRouter, Redirect } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect, useState, useRef } from 'react';
import { AppProvider, useAppContext } from '../context/AppContext';
import { Text } from 'react-native';
import React from 'react';

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    PoppinsRegular: require('../assets/fonts/Poppins-Regular.ttf'),
    PoppinsMedium: require('../assets/fonts/Poppins-Medium.ttf'),
    PoppinsBold: require('../assets/fonts/Poppins-Bold.ttf'),
    ...FontAwesome.font,
  });
  const [fontTimeout, setFontTimeout] = useState(false);

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Add a timeout for font loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!loaded) {
        setFontTimeout(true);
        console.warn('Font loading timeout!');
      }
    }, 3000);
    return () => clearTimeout(timer);
  }, [loaded]);

  if (!loaded && !fontTimeout) {
    return null;
  }

  if (fontTimeout && !loaded) {
    return (
      <Text style={{ color: 'red', marginTop: 100, fontSize: 20 }}>
        Font loading failed or is taking too long!
      </Text>
    );
  }

  return (
    <AppProvider>
      <RootLayoutNav />
    </AppProvider>
  );
}

function RootLayoutNav() {
  const { user } = useAppContext();





  // Route protection logic moved into its own effect to avoid calling hook inside hook
  const segments = useSegments();
  const router = useRouter();
  const processingRef = React.useRef(false);
  const prevSegmentsRef = React.useRef<string[] | null>(null);
  const prevUserRef = React.useRef(user);
  const navigationTimerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    console.log('🔄 Navigation useEffect triggered:', {
      segments,
      user: !!user,
      processing: processingRef.current
    });

    // Skip if we're already processing to prevent multiple redirects
    if (processingRef.current) {
      console.log('⏸️ Skipping navigation - already processing');
      return;
    }

    // Add a small delay to ensure user state is stable
    const navigationTimer = setTimeout(() => {
      // Skip if nothing has changed since last check (but always run on first call)
      if (
        prevSegmentsRef.current !== null &&
        JSON.stringify(segments) === JSON.stringify(prevSegmentsRef.current) &&
        user === prevUserRef.current
      ) {
        console.log('⏸️ Skipping navigation - no changes detected');
        return;
      }

      console.log('🚀 Navigation conditions met, proceeding with navigation logic');

      // Update refs
      prevSegmentsRef.current = segments;
      prevUserRef.current = user;

      // Check if we're in the auth group
      const inAuthGroup = segments[0] === '(auth)';

      console.log('🔍 Navigation Debug - Current state:', {
        user: !!user,
        inAuthGroup,
        currentSegment: segments[0],
        allSegments: segments,
        onboardingCompleted: user?.onboardingCompleted
      });

      // Set processing flag to prevent multiple redirects
      processingRef.current = true;

      try {
        // If user is not signed in and not in auth/onboarding/modals flow, redirect to welcome
        if (!user && !inAuthGroup && segments[0] !== '(onboarding)' && segments[0] !== '(modals)') {
          console.log('🚀 NEW FLOW: Redirecting unauthenticated user to welcome screen from:', segments[0]);
          router.replace('/(onboarding)/welcome');
          console.log('✅ router.replace call completed');
        // If user is signed in and in auth group, redirect based on onboarding status
        } else if (user && inAuthGroup) {
          console.log('✅ User authenticated, checking onboarding status');

          // Check if user has completed onboarding
          if (user.onboardingCompleted) {
            // User has completed onboarding, go to main app
            router.replace('/(tabs)');
            console.log('✅ Authenticated user with completed onboarding redirected to tabs');
          } else {
            // User needs to complete onboarding (set preferences)
            router.replace('/(modals)/filters');
            console.log('✅ Authenticated user redirected to filters for onboarding');
          }
        // If user is authenticated but somehow ended up on welcome screen, redirect appropriately
        } else if (user && segments[0] === '(onboarding)') {
          console.log('🔄 Authenticated user on welcome screen, redirecting based on onboarding status');

          if (user.onboardingCompleted) {
            router.replace('/(tabs)');
            console.log('✅ Authenticated user with completed onboarding redirected from welcome to tabs');
          } else {
            router.replace('/(modals)/filters');
            console.log('✅ Authenticated user redirected from welcome to filters for onboarding');
          }
        } else {
          console.log('🔄 No navigation needed - staying on current screen:', segments[0]);
        }
      } catch (navError) {
        console.error('Navigation error:', navError);
      } finally {
        // Reset processing flag after a short delay to allow navigation to complete
        setTimeout(() => {
          processingRef.current = false;
        }, 500);
      }
    }, 100); // Small delay to ensure state stability

    // Store timer ref for cleanup
    navigationTimerRef.current = navigationTimer;

    return () => {
      if (navigationTimerRef.current) {
        clearTimeout(navigationTimerRef.current);
      }
    };
  }, [user, segments]); // Removed isLoading dependency to prevent unnecessary re-runs

  // Removed loading screen - users go directly to their dedicated screen

  // Synchronous redirect to avoid initial flicker to tabs/home
  const inAuthGroup = segments[0] === '(auth)';
  const inOnboarding = segments[0] === '(onboarding)';
  const inModals = segments[0] === '(modals)';
  
  if (!user && !inAuthGroup && !inOnboarding && !inModals) {
    return <Redirect href="/(onboarding)/welcome" />;
  }
  
  if (user && inAuthGroup) {
    // Check onboarding status for immediate redirect
    if (user.onboardingCompleted) {
      return <Redirect href="/(tabs)" />;
    } else {
      return <Redirect href="/(modals)/filters" />;
    }
  }
  
  // If user is authenticated but somehow ended up on welcome screen, redirect appropriately
  if (user && inOnboarding) {
    if (user.onboardingCompleted) {
      return <Redirect href="/(tabs)" />;
    } else {
      return <Redirect href="/(modals)/filters" />;
    }
  }

  // Return a Slot instead of conditional Stack to fix the warning
  return <Slot />;
}
